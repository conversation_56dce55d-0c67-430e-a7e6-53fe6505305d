import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as { email?: string };
    const { email } = body;

    // Validate email
    if (!email) {
      return NextResponse.json(
        { message: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if email is valid format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { message: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check if email already exists in the system
    // In a real application, you would check against a database
    // For demonstration purposes, we'll assume the email is valid

    // Generate a random verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // In a real application, you would:
    // 1. Store the verification code in a database or cache with an expiration time
    // 2. Send the verification code to the user's email
    
    // For demonstration purposes, we'll just return a success response
    // In a real application, you wouldn't return the verification code in the response
    
    // TODO: Add actual email sending integration
    
    console.log(`Verification code for ${email}: ${verificationCode}`);

    return NextResponse.json(
      { 
        message: 'Verification code sent successfully',
        // In a production environment, you would NOT include the code in the response
        // This is only for demonstration purposes
        debug: { code: verificationCode }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Send verification code error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
