import { Metadata } from "next"
import SponsorshipClient from "@/components/sponsorship/sponsorship-client"

export const metadata: Metadata = {
  title: "Sponsorship & Exhibition | IFMB 2025",
  description:
    "Sponsorship and exhibition opportunities for the International Forum on Maize Biology 2025. Support the premier maize biology conference.",
  keywords: "IFMB sponsorship, maize biology exhibition, conference sponsorship, IFMB 2025 sponsors, agricultural conference support",
}

/**
 * Sponsorship page for IFMB 2025
 */
export default function SponsorshipPage() {
  // Conference information
  const conferenceInfo = {
    title: "International Forum on Maize Biology",
    shortTitle: "IFMB 2025",
    dates: "October 16-20, 2025",
    location: "Wuhan, Hubei Province, China",
    venue: "Huazhong Agricultural University",
    expectedAttendance: "500+",
    organizers: [
      "Huazhong Agricultural University (HZAU)",
      "HZAU College of Life Science & Technology",
      "HZAU College of Plant Science & Technology",
      "National Key Laboratory of Crop Genetic Improvement",
      "Hubei Hongshan Laboratory"
    ]
  }

  // Exhibition booth options
  const exhibitionBooths = [
    {
      id: "A",
      name: "Standard Booth",
      dimensions: "2m × 2m × 2.48m",
      price: 30000,
      currency: "CNY",
      features: [
        "1 power outlet",
        "1 table",
        "2 chairs"
      ]
    },
    {
      id: "B", 
      name: "Large Booth",
      dimensions: "4m × 2m × 2.48m",
      price: 60000,
      currency: "CNY",
      features: [
        "Enhanced space",
        "Premium location options"
      ]
    },
    {
      id: "C",
      name: "Extra-Large Booth", 
      dimensions: "6m × 2m × 2.48m",
      price: 80000,
      currency: "CNY",
      features: [
        "Maximum exhibition space",
        "Prime location"
      ]
    }
  ]

  // Promotional materials options
  const promotionalOptions = [
    {
      id: "color-ad",
      name: "Color Advertisement in Abstract Compendium",
      price: 10000,
      currency: "CNY",
      unit: "per page"
    },
    {
      id: "logo-display",
      name: "Logo Display on Conference Website and Abstract Compendium",
      price: 10000,
      currency: "CNY"
    },
    {
      id: "brochure",
      name: "Product Brochure in Conference Bag",
      description: "A4 size, ≤2 pages",
      price: 10000,
      currency: "CNY"
    }
  ]

  // Sponsorship packages
  const sponsorshipPackages = [
    {
      id: "package-1",
      name: "Package I",
      price: 40000,
      currency: "CNY",
      features: [
        "One standard booth (2m × 2m × 2.48m)",
        "Includes 1 power outlet, 1 table, 2 chairs",
        "One color advertisement page in abstract compendium",
        "Logo display on conference website and in abstract compendium",
        "One complimentary conference registration",
        "Includes abstract compendium",
        "Travel and accommodation at sponsor's expense"
      ]
    },
    {
      id: "package-2", 
      name: "Package II",
      price: 80000,
      currency: "CNY",
      features: [
        "One large booth (4m × 2m × 2.48m)",
        "Product brochure in conference bag (A4 size, ≤3 pages)",
        "One color advertisement page in abstract compendium",
        "Logo display on conference website",
        "Two complimentary conference registrations",
        "Include abstract compendium",
        "Travel and accommodation at sponsor's expense"
      ]
    },
    {
      id: "co-organizer",
      name: "Co-Organizer Sponsorship",
      price: 150000,
      currency: "CNY",
      features: [
        "Designation as 'Co-Organizer'",
        "Name/logo on main conference backdrop",
        "One customized large booth (4m × 2m × 2.48m)",
        "Priority location selection",
        "Logo display on conference website and program handbook",
        "Two complimentary conference registrations",
        "Include abstract compendium",
        "Travel and accommodation at sponsor's expense"
      ],
      highlighted: true
    }
  ]

  // Contact information
  const contacts = [
    {
      name: "Yingjie Xiao",
      phone: "+86 ***********",
      email: "<EMAIL>"
    },
    {
      name: "Fazhan Qiu", 
      phone: "+86 ***********",
      email: "<EMAIL>"
    }
  ]

  // Payment information
  const paymentInfo = {
    international: {
      beneficiary: "Huazhong Agricultural University",
      accountNumber: "************",
      bank: "Bank of China Hubei Branch",
      swiftCode: "BKCHCNBJ600",
      address: "Bank of China Hubei Branch, Donghu Subbranch 430079"
    },
    domestic: {
      beneficiary: "Huazhong Agricultural University",
      bank: "Bank of China Hubei Branch, Huanong Subbranch",
      beneficiaryNumber: "************",
      account: "************",
      address: "1 Shizishan St, Hongshan District, Wuhan 430070, China"
    }
  }

  // Important dates and notes
  const importantInfo = {
    deadline: "September 20, 2025",
    paymentDeadline: "10 days after confirmation",
    totalBooths: "30-35 exhibition booths available",
    notes: [
      "Booth locations will be allocated based on payment receipt order after agreement signing",
      "Roll-up banners and displays must be confined within booths to ensure exhibition safety",
      "Additional requests require prior negotiation with the organizing committee; costs will be determined separately"
    ]
  }

  return (
    <SponsorshipClient
      conferenceInfo={conferenceInfo}
      exhibitionBooths={exhibitionBooths}
      promotionalOptions={promotionalOptions}
      sponsorshipPackages={sponsorshipPackages}
      contacts={contacts}
      paymentInfo={paymentInfo}
      importantInfo={importantInfo}
    />
  )
}
