"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { useEffect, useState } from "react"
import CountdownTimer from "@/components/home/<USER>"
import DateCard from "@/components/home/<USER>"
import KeySpeakersCarousel from "@/components/home/<USER>"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { fadeIn, staggerContainer } from "@/lib/animations"

// Types
type ConferenceData = {
  title: string
  shortTitle: string
  dates: string
  location: string
  city: string
  conferenceDate: string
  registrationDeadline: string
  abstractDeadline: string
  earlyBirdDeadline: string
}

type Speaker = {
  name: string
  title: string
  institution: string
  image?: string
  topic?: string
}

type ImportantDate = {
  title: string
  date: string
  icon: string
  color: string
  description: string
}

type HomePageProps = {
  conferenceData: ConferenceData
  keynoteSpeakers: Speaker[]
  importantDates: ImportantDate[]
}

export default function HomePage({ conferenceData, keynoteSpeakers, importantDates }: HomePageProps) {
  const [, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  return (
    <main className="overflow-hidden">
      {/* Hero Section - Image Only */}
      <section className="relative h-screen overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="/images/background.jpg"
            alt="Conference Background"
            fill
            priority
            className="object-cover object-top"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-transparent to-blue-900/50"></div>
          <div className="absolute inset-0 bg-[url('/images/pattern.svg')] opacity-10"></div>
        </div>

        <div className="square:pt-12 absolute top-1/2 left-1/2 z-10 -translate-x-1/2 -translate-y-1/2 transform pt-16 text-center">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-4 text-5xl font-bold text-white drop-shadow-lg md:text-6xl lg:text-7xl"
          >
            {conferenceData.shortTitle}
          </motion.h1>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="square:gap-2 flex flex-col items-center gap-3"
          >
            <Badge className="square:py-1.5 bg-yellow-500 px-4 py-2 text-lg font-medium tracking-wide text-white shadow-lg">
              {conferenceData.dates}
            </Badge>
            <Badge className="square:py-1.5 square:text-sm bg-blue-600/80 px-4 py-2 text-base font-medium tracking-wide text-white shadow-lg backdrop-blur-sm">
              {conferenceData.city}
            </Badge>
          </motion.div>
        </div>

        <div className="absolute right-0 bottom-8 left-0 z-20 flex justify-center">
          <motion.div
            initial={{ y: -10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{
              duration: 1.2,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
            }}
          >
            <a
              href="#welcome"
              className="flex flex-col items-center rounded-full bg-black/30 px-4 py-2 text-white backdrop-blur-sm transition-colors duration-300 hover:text-yellow-300"
            >
              <span className="mb-2 text-sm font-medium">Scroll Down</span>
              <i className="fas fa-chevron-down text-xl"></i>
            </a>
          </motion.div>
        </div>
      </section>

      {/* Welcome Section - Content Below Image */}
      <section id="welcome" className="bg-white py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.2)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.3)} className="mb-8 text-center">
              <Badge className="mb-4 bg-green-100 px-3 py-1 text-green-800">
                {conferenceData.location}, {conferenceData.city}
              </Badge>
              <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">{conferenceData.title}</h2>
            </motion.div>

            <motion.p variants={fadeIn("up", 0.4)} className="mb-6 text-justify text-lg leading-relaxed text-gray-700">
              It is with great excitement and pride that we invite you to the inaugural International Forum on Maize
              Biology (IFMB 2025), a landmark event hosted by Huazhong Agricultural University at the International
              Academic Exchange Center in Wuhan, China, from October 16–20, 2025. This premier scientific gathering
              represents a unique opportunity to unite the global maize research community in advancing solutions to
              some of the most pressing challenges in agriculture and food security.
            </motion.p>

            <motion.p variants={fadeIn("up", 0.5)} className="mb-6 text-justify text-lg leading-relaxed text-gray-700">
              Over five dynamic days, IFMB 2025 will bring together over 40 distinguished speakers from the world's
              leading institutions across Europe, North America, and Asia, alongside researchers at all career
              stages—from PhD students and postdocs to established scientists. The forum will showcase cutting-edge
              advances in maize biology, with in-depth scientific sessions (October 16–18) covering genomics,
              development, metabolism, and next-generation breeding technologies, followed by immersive research
              activities in Wuhan on October 19.
            </motion.p>

            <motion.p variants={fadeIn("up", 0.55)} className="mb-6 text-justify text-lg leading-relaxed text-gray-700">
              We have designed IFMB 2025 to be more than just a conference—it is a collaborative platform for sparking
              innovation, sharing breakthroughs, and forging lasting international partnerships. Whether you are
              investigating maize resilience under climate change, pioneering genomic tools, or developing sustainable
              crop solutions, your participation will help shape the future of this vital field.
            </motion.p>

            <motion.p variants={fadeIn("up", 0.6)} className="mb-6 text-justify text-lg leading-relaxed text-gray-700">
              Join us in Wuhan, a vibrant hub of science and culture, to connect with peers, engage in lively
              discussions, and contribute to a collective vision for maize research that benefits humanity. Together, we
              can drive progress toward a more food-secure world.
            </motion.p>

            <motion.div variants={fadeIn("up", 0.65)} className="mb-10 flex justify-center">
              <CountdownTimer targetDate={conferenceData.conferenceDate} />
            </motion.div>

            <motion.div variants={fadeIn("up", 0.7)} className="flex flex-col justify-center gap-4 sm:flex-row">
              <Link href="/register">
                <Button className="transform cursor-pointer !rounded-lg bg-yellow-500 px-6 py-4 text-base font-semibold whitespace-nowrap text-white shadow-md transition-all duration-300 hover:-translate-y-1 hover:bg-yellow-600">
                  Register Now
                </Button>
              </Link>
              <Link href="/console/submit">
                <Button
                  variant="outline"
                  className="transform cursor-pointer !rounded-lg border-2 border-green-500 bg-white px-6 py-4 text-base font-semibold whitespace-nowrap text-green-600 shadow-md transition-all duration-300 hover:-translate-y-1 hover:bg-green-50"
                >
                  Submit Abstract <i className="fas fa-arrow-right ml-2"></i>
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="bg-gray-50 py-20">
        <div className="container mx-auto px-6">
          <div className="mb-16 text-center">
            <Badge className="mb-3 bg-green-100 px-3 py-1 text-green-800">About the Conference</Badge>
            <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
              Advancing Maize Research to Enhance Global Food Security
            </h2>
            <p className="mx-auto mb-6 max-w-4xl text-justify text-lg text-gray-600">
              The inaugural International Forum on Maize Biology 2025 serves as a groundbreaking platform, bringing
              together more than 35 distinguished speakers from premier research institutions across Europe, North
              America, and Asia. These leading experts will share their latest findings and innovations in maize
              genetics, genomics, stress resilience, and sustainable cultivation practices. By fostering
              interdisciplinary collaboration, the forum aims to accelerate breakthroughs in maize research, addressing
              critical challenges such as climate adaptation, yield optimization, and nutritional enhancement. As maize
              remains a cornerstone of global agriculture, this event underscores its vital role in ensuring long-term
              food security for a growing population. Through keynote lectures, panel discussions, and interactive
              workshops, participants will explore cutting-edge advancements that could revolutionize maize production
              and contribute to a more resilient and equitable food system worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3">
            <Card className="rounded-xl border-none bg-gradient-to-br from-yellow-50 to-white p-8 shadow-lg transition-shadow hover:shadow-xl">
              <div className="mb-6 flex h-14 w-14 items-center justify-center rounded-full bg-yellow-100 text-yellow-600">
                <i className="fas fa-microscope text-2xl"></i>
              </div>
              <h3 className="mb-3 text-xl font-bold text-gray-900">Comprehensive Scientific Program</h3>
              <p className="mb-4 text-gray-600">
                Explore cutting-edge advances in maize genomics, development, metabolism, and breeding technologies
                through sessions from October 16-18, 2025.
              </p>
            </Card>

            <Card className="rounded-xl border-none bg-gradient-to-br from-green-50 to-white p-8 shadow-lg transition-shadow hover:shadow-xl">
              <div className="mb-6 flex h-14 w-14 items-center justify-center rounded-full bg-green-100 text-green-600">
                <i className="fas fa-users text-2xl"></i>
              </div>
              <h3 className="mb-3 text-xl font-bold text-gray-900">Distinguished International Speakers</h3>
              <p className="mb-4 text-gray-600">
                Learn from over 35 renowned experts from leading institutions across Europe, North America, and Asia
                sharing their latest research findings.
              </p>
            </Card>

            <Card className="rounded-xl border-none bg-gradient-to-br from-blue-50 to-white p-8 shadow-lg transition-shadow hover:shadow-xl">
              <div className="mb-6 flex h-14 w-14 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                <i className="fas fa-handshake text-2xl"></i>
              </div>
              <h3 className="mb-3 text-xl font-bold text-gray-900">Networking & Research Activities</h3>
              <p className="mb-4 text-gray-600">
                Foster international collaborations through scientific sessions, followed by special research activities
                in Wuhan on October 19, 2025.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Important Dates */}
      <section className="square:py-10 bg-white py-20">
        <div className="container mx-auto px-6">
          <div className="square:mb-8 mb-16 text-center">
            <Badge className="mb-3 bg-green-100 px-3 py-1 text-green-800">Mark Your Calendar</Badge>
            <h2 className="square:text-2xl mb-4 text-3xl font-bold text-gray-900 md:text-4xl">Important Dates</h2>
            <p className="square:text-base mx-auto max-w-3xl text-lg text-gray-600">
              Don't miss these critical deadlines for participation in IFMB 2025.
            </p>
          </div>

          <div className="square:flex-col square:gap-6 flex flex-col gap-8 lg:flex-row">
            <div className="square:w-full flex lg:w-1/2">
              <div className="square:gap-6 grid flex-grow grid-cols-1 gap-8 md:grid-cols-2">
                {importantDates.map((date, index) => (
                  <DateCard key={index} date={date} index={index} />
                ))}
              </div>
            </div>

            <div className="square:w-full flex lg:w-1/2">
              <div className="square:mt-0 flex-grow overflow-hidden rounded-xl bg-white shadow-md">
                <div className="square:p-4 p-6">
                  <h3 className="square:mb-4 square:text-lg mb-6 flex items-center text-xl font-bold text-gray-900">
                    <span className="mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 text-amber-600">
                      <i className="fas fa-calendar-alt"></i>
                    </span>
                    Conference Schedule
                  </h3>

                  <div className="square:space-y-4 space-y-6">
                    <div className="flex items-start">
                      <div className="square:h-8 square:w-8 square:mr-3 mr-4 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 text-amber-600">
                        <i className="fas fa-plane-arrival"></i>
                      </div>
                      <div>
                        <h3 className="square:text-base text-lg font-semibold text-gray-800">October 16, 2025</h3>
                        <p className="square:text-sm text-gray-600">Arrival day and evening welcome reception</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="square:h-8 square:w-8 square:mr-3 mr-4 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 text-amber-600">
                        <i className="fas fa-calendar-day"></i>
                      </div>
                      <div>
                        <h3 className="square:text-base text-lg font-semibold text-gray-800">October 17-19, 2025</h3>
                        <p className="square:text-sm text-gray-600">Main conference sessions and presentations</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="square:h-8 square:w-8 square:mr-3 mr-4 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 text-amber-600">
                        <i className="fas fa-microscope"></i>
                      </div>
                      <div>
                        <h3 className="square:text-base text-lg font-semibold text-gray-800">October 19, 2025</h3>
                        <p className="square:text-sm text-gray-600">Survey and research activities in Wuhan</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="square:h-8 square:w-8 square:mr-3 mr-4 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 text-amber-600">
                        <i className="fas fa-plane-departure"></i>
                      </div>
                      <div>
                        <h3 className="square:text-base text-lg font-semibold text-gray-800">October 20, 2025</h3>
                        <p className="square:text-sm text-gray-600">Departure days</p>
                      </div>
                    </div>
                  </div>

                  <div className="square:mt-6 square:pt-4 mt-8 border-t border-gray-200 pt-6">
                    <p className="square:text-xs text-sm text-gray-500 italic">
                      <i className="fas fa-info-circle mr-2"></i>
                      Full schedule: Arrival on Oct 16th, meeting from evening Oct 16th to 18th, survey and research in
                      Wuhan on Oct 19th, departure on Oct 19th-20th.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Keynote Speakers */}
      <section className="bg-gray-50 py-20">
        <div className="container mx-auto px-6">
          <div className="mb-16 text-center">
            <Badge className="mb-3 bg-green-100 px-3 py-1 text-green-800">World-Class Experts</Badge>
            <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">Keynote Speakers</h2>
            <p className="mx-auto max-w-3xl text-lg text-gray-600">
              Learn from distinguished researchers who are shaping the future of corn biology and agricultural science.
            </p>
          </div>

          <KeySpeakersCarousel speakers={keynoteSpeakers} />

          <div className="mt-12 text-center">
            <Link href="/reporters">
              <Button className="transform rounded-lg bg-green-600 px-6 py-3 text-white shadow-md transition-all duration-300 hover:-translate-y-1 hover:bg-green-700">
                View All Speakers <i className="fas fa-arrow-right ml-2"></i>
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Cooperation Platform */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <div className="mb-12 text-center">
            <Badge className="mb-3 bg-green-100 px-3 py-1 text-green-800">Partnerships</Badge>
            <h2 className="mb-4 text-3xl font-bold text-gray-900">Cooperation Platform</h2>
            <p className="mx-auto max-w-3xl text-gray-600">
              IFMB 2025 collaborates with leading institutions and organizations to advance maize biology research
            </p>
          </div>

          <div className="grid grid-cols-2 items-center justify-items-center gap-8 md:grid-cols-3 lg:grid-cols-5">
            {/* Placeholder for partner logos */}
            {[1, 2, 3, 4, 5].map((i) => (
              <div
                key={i}
                className="flex h-32 w-full items-center justify-center rounded-lg bg-white p-6 shadow-md transition-all duration-300 hover:shadow-lg"
              >
                <div className="text-4xl font-light text-gray-300">Partner {i}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partner Media */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <div className="mb-12 text-center">
            <Badge className="mb-3 bg-green-100 px-3 py-1 text-green-800">Media</Badge>
            <h2 className="mb-4 text-3xl font-bold text-gray-900">Partner Media</h2>
            <p className="mx-auto max-w-3xl text-gray-600">
              Our media partners help us spread the word about the latest developments in maize biology
            </p>
          </div>

          <div className="grid grid-cols-2 items-center justify-items-center gap-8 md:grid-cols-3 lg:grid-cols-4">
            {/* Placeholder for media partner logos */}
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="flex h-24 w-full items-center justify-center rounded-lg border border-gray-100 bg-white p-6 shadow-md transition-all duration-300 hover:shadow-lg"
              >
                <div className="text-3xl font-light text-gray-300">Media {i}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Us */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <div className="mb-12 text-center">
            <Badge className="mb-3 bg-green-100 px-3 py-1 text-green-800">Get in Touch</Badge>
            <h2 className="mb-4 text-3xl font-bold text-gray-900">Contact Us</h2>
            <p className="mx-auto max-w-3xl text-gray-600">Have questions about IFMB 2025? We're here to help!</p>
          </div>

          <div className="mx-auto max-w-4xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {/* Contact Information */}
              <div className="rounded-lg bg-white p-8 shadow-md">
                <h3 className="mb-6 text-xl font-semibold text-gray-800">Contact Information</h3>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="mr-4 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-green-100 text-green-600">
                      <i className="fas fa-envelope"></i>
                    </div>
                    <div>
                      <p className="mb-1 text-sm text-gray-500">Email</p>
                      <p className="font-medium text-gray-700"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="mr-4 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-green-100 text-green-600">
                      <i className="fas fa-map-marker-alt"></i>
                    </div>
                    <div>
                      <p className="mb-1 text-sm text-gray-500">Address</p>
                      <p className="font-medium text-gray-700">
                        1st Shizi Shan St, International Academic Exchange Center of Huazhong Agricultural University,
                        Hongshan District, Wuhan, China
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="rounded-lg bg-white p-8 shadow-md">
                <h3 className="mb-6 text-xl font-semibold text-gray-800">Send us a Message</h3>

                <div className="space-y-4">
                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">Your Name</label>
                    <input
                      type="text"
                      className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-green-500 focus:ring-green-500"
                      placeholder="Enter your name"
                    />
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">Email Address</label>
                    <input
                      type="email"
                      className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-green-500 focus:ring-green-500"
                      placeholder="Enter your email"
                    />
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">Message</label>
                    <textarea
                      rows={4}
                      className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-green-500 focus:ring-green-500"
                      placeholder="Enter your message"
                    ></textarea>
                  </div>

                  <Button className="w-full rounded-md bg-green-600 py-2 text-white transition-colors duration-300 hover:bg-green-700">
                    Send Message
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-green-700 to-green-600 py-20 text-white">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="mb-6 text-3xl font-bold md:text-4xl">Join Us at IFMB 2025</h2>
            <p className="mb-10 text-xl text-green-100">
              Be part of the premier international conference on corn biology and help shape the future of agricultural
              science.
            </p>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Link href="/register">
                <Button className="transform rounded-lg bg-white px-8 py-4 text-lg font-semibold text-green-700 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:bg-green-100">
                  Register Now
                </Button>
              </Link>
              <Link href="/organization">
                <Button
                  variant="outline"
                  className="transform rounded-lg border-2 border-white bg-transparent px-8 py-4 text-lg font-semibold shadow-lg transition-all duration-300 hover:-translate-y-1 hover:bg-white/10"
                >
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
