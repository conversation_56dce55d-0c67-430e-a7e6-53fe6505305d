"use client"

import React, { useState, useEffect } from 'react'
import Image from "next/image"
import Link from "next/link"
import { Header } from './header'

// 使用一次性渲染的Header包装器
export default function ClientOnlyHeader() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // 在服务端渲染和客户端挂载前显示占位符Header
  if (!mounted) {
    return (
      <header className="fixed top-0 right-0 left-0 z-50 transition-all duration-300 bg-black/20 py-5 backdrop-blur-sm">
        <div className="container mx-auto flex items-center justify-between px-6">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full border-2 border-yellow-400 bg-white overflow-hidden shadow-md transition-all duration-300 hover:scale-105">
              <Image src="/favicon.ico" alt="IFMB Logo" width={24} height={24} className="object-contain" />
            </div>
            <h1 className="font-bold tracking-tight text-white transition-colors duration-300">
              <span className="hidden sm:inline">International Forum on </span>
              <span className="text-amber-400" style={{ whiteSpace: "nowrap" }}>
                Maize Biology
              </span>
            </h1>
          </Link>

          {/* Desktop Navigation Placeholder */}
          <div className="hidden items-center space-x-8 lg:flex">
            <div className="flex space-x-4">
              <div className="h-10 w-16 rounded-lg bg-white/10 animate-pulse"></div>
              <div className="h-10 w-24 rounded-lg bg-white/10 animate-pulse"></div>
              <div className="h-10 w-20 rounded-lg bg-white/10 animate-pulse"></div>
              <div className="h-10 w-16 rounded-lg bg-white/10 animate-pulse"></div>
            </div>
            <div className="h-10 w-20 rounded-lg bg-white/10 animate-pulse"></div>
          </div>

          {/* Mobile Menu Button Placeholder */}
          <div className="lg:hidden">
            <div className="h-10 w-10 rounded bg-white/10 animate-pulse"></div>
          </div>
        </div>
      </header>
    )
  }

  return <Header />
}
