"use client"

import Image from "next/image"
import Link from "next/link"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="relative overflow-hidden bg-gray-900 text-white">
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 h-full w-1/3 bg-gradient-to-l from-green-900/10 to-transparent"></div>
      <div className="absolute bottom-0 left-0 h-1/2 w-1/2 bg-gradient-to-t from-amber-900/10 to-transparent"></div>

      {/* Main footer content */}
      <div className="relative z-10 container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 gap-10 md:grid-cols-3">
          {/* Column 1: About */}
          <div>
            <div className="mb-6 flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-full border-2 border-yellow-500 bg-white shadow-md transition-all duration-300 hover:scale-105">
                <Image src="/favicon.ico" alt="IFMB Logo" width={24} height={24} className="object-contain" />
              </div>
              <h3 className="text-lg font-bold">
                International Forum on <span className="text-yellow-400">Maize Biology</span>
              </h3>
            </div>
            <p className="mb-6 text-gray-400">
              The inaugural International Forum on Maize Biology (IFMB 2025) will be held at the International Academic
              Exchange Center, Huazhong Agricultural University in Wuhan, China, from October 16-20, 2025. This five-day
              event brings together over 35 distinguished speakers from leading institutions across Europe, North
              America, and Asia.
            </p>
          </div>

          {/* Column 2: Important Dates */}
          <div>
            <h4 className="relative mb-5 inline-block text-lg font-semibold after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-10 after:bg-yellow-500">
              Important Dates
            </h4>
            <ul className="space-y-3">
              <li className="flex items-start text-gray-400">
                <i className="fas fa-file-alt mt-1 mr-2 text-green-500"></i>
                <span>Abstract Submission: June 15, 2025</span>
              </li>
              <li className="flex items-start text-gray-400">
                <i className="fas fa-calendar-alt mt-1 mr-2 text-green-500"></i>
                <span>Registration Deadline: Sept. 15, 2025</span>
              </li>
              <li className="flex items-start text-gray-400">
                <i className="fas fa-credit-card mt-1 mr-2 text-green-500"></i>
                <span>Payment Deadline: Sept. 15, 2025</span>
              </li>
              <li className="flex items-start text-gray-400">
                <i className="fas fa-user-check mt-1 mr-2 text-green-500"></i>
                <span>On-site Registration: June 15, 2025</span>
              </li>
              <li className="flex items-start text-gray-400">
                <i className="fas fa-calendar-week mt-1 mr-2 text-green-500"></i>
                <span>Conference Dates: October 16-20, 2025</span>
              </li>
            </ul>
          </div>

          {/* Column 3: Contact */}
          <div>
            <h4 className="relative mb-5 inline-block text-lg font-semibold after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-10 after:bg-yellow-500">
              Contact Us
            </h4>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gray-800">
                  <i className="fas fa-envelope text-green-500"></i>
                </div>
                <div>
                  <p className="text-gray-400"><EMAIL></p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gray-800">
                  <i className="fas fa-map-marker-alt text-green-500"></i>
                </div>
                <div>
                  <p className="text-gray-400">
                    1st Shizi Shan St, International Academic Exchange Center of Huazhong Agricultural University,
                    Hongshan District, Wuhan, China
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Copyright */}
      <div className="relative z-10 border-t border-gray-800 py-8">
        <div className="container mx-auto px-6">
          <div className="flex flex-col items-center justify-between md:flex-row">
            <p className="mb-4 text-sm text-gray-500 md:mb-0">
              &copy; {currentYear} International Forum on Maize Biology. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link href="/privacy" className="text-sm text-gray-500 transition-colors hover:text-green-400">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-sm text-gray-500 transition-colors hover:text-green-400">
                Terms & Conditions
              </Link>
              <Link href="/cookies" className="text-sm text-gray-500 transition-colors hover:text-green-400">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
