"use client"

import * as React from "react"; // Added to fix lint error
import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useEffect, useState, useMemo, useCallback, memo, useRef } from "react"
import { useUser } from "@/components/layout/user-context"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { cn } from "@/lib/utils"

// ListItem component for styled dropdown items - memoized to prevent unnecessary re-renders
const ListItem = React.memo(React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a">
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  )
}))
ListItem.displayName = "ListItem"

// 模拟用户类型
type User = {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'user' | 'admin'
  emailVerified?: boolean
}

// 定义并导出Header组件 - 使用 memo 优化性能，确保只渲染一次
const Header = memo(function Header() {
  const pathname = usePathname()
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const router = useRouter() // 获取 router 实例

  // 使用 UserContext 来获取用户状态，无需加载状态
  const { user, logout: contextLogout } = useUser()

  // 只在开发环境下输出调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('Header: rendered or re-rendered. Pathname:', pathname);
  }

  // 检查当前页面是否为认证相关页面
  const isAuthPage = ["/register", "/login", "/forgot-password"].includes(pathname)

  // 检查当前页面是否需要不透明 header (verify-email 页面保持透明)
  const needsSolidHeader = isAuthPage || pathname?.startsWith("/profile") || pathname?.startsWith("/dashboard")

  // 简化的组件挂载日志
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Header: MOUNTED. Pathname on mount:', pathname);
    }

    return () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Header: UNMOUNTED. Pathname on unmount:', pathname);
      }
    };
  }, []); // 只在挂载和卸载时运行



  // 登出功能 - 使用 Context 中的 logout 方法
  const handleLogout = () => {
    contextLogout()
    router.push('/') // 使用router.push进行客户端导航
  }

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    // Set initial state on mount
    handleScroll();

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [])

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])


  // Navigation configuration for desktop menu - memoized to prevent recreation on re-renders
  const desktopNavConfig = useMemo(() => [
    { name: "Home", href: "/" }, // Direct link
    {
      name: "Organization", // Used for key
      triggerText: "Organization",
      items: [
        { title: "Executive Committee", href: "/executive", description: "Meet the executive committee." },
        { title: "Organizing Committee", href: "/organization", description: "Meet the organizing committee." },
      ],
    },
    {
      name: "Conference",
      triggerText: "Conference",
      items: [
        { title: "Schedule", href: "/schedule", description: "View the conference schedule." },
        { title: "Transportation", href: "/transportation", description: "Information on location and travel." },
      ],
    },
    {
      name: "More",
      triggerText: "More",
      items: [
        { title: "Sponsorship", href: "/sponsorship", description: "Support the conference." },
      ],
    },
  ], []);
  
  // Helper function to determine link class names
  const getLinkClassName = useCallback((href: string, isListItem: boolean = false) => {
    const isActive = pathname === href;
    const baseTriggerStyles = navigationMenuTriggerStyle();

    const activeStyle = isScrolled || needsSolidHeader 
        ? "bg-amber-500/10 text-amber-600 font-semibold" 
        : "bg-amber-500/20 text-amber-400 font-semibold";
    
    const listItemActiveStyle = isScrolled || needsSolidHeader
        ? "bg-accent text-accent-foreground font-semibold" // Active style for ListItem
        : "bg-white/20 text-amber-300 font-semibold"; // Active style for ListItem on transparent header

    if (isListItem) {
      return cn(
        isActive ? listItemActiveStyle : ""
      );
    }

    return cn(
      baseTriggerStyles,
      isScrolled || needsSolidHeader
        ? "text-gray-700 hover:text-amber-600 bg-transparent hover:bg-gray-100"
        : "text-white/90 hover:text-white bg-transparent hover:bg-white/10",
      isActive ? activeStyle : ""
    );
  }, [pathname, isScrolled, needsSolidHeader]);

  // Helper function for trigger class names (to show active state if a child is active)
  const getTriggerClassName = useCallback((itemGroup: { items: Array<{ href: string }> }) => {
    const isDropdownActive = itemGroup.items.some(subItem => pathname === subItem.href);
    const activeStyle = isScrolled || needsSolidHeader 
        ? "bg-amber-500/10 text-amber-600 font-semibold" 
        : "bg-amber-500/20 text-amber-400 font-semibold";

    return cn(
      navigationMenuTriggerStyle(),
      isScrolled || needsSolidHeader
        ? "text-gray-700 hover:text-amber-600 bg-transparent hover:bg-gray-100"
        : "text-white/90 hover:text-white bg-transparent hover:bg-white/10",
      isDropdownActive ? activeStyle : ""
    );
  }, [pathname, isScrolled, needsSolidHeader]);
  
  // 使用useMemo缓存桌面导航菜单，防止页面切换时重新加载
  const memoizedDesktopMenu = useMemo(() => (
    <NavigationMenu>
      <NavigationMenuList>
        {desktopNavConfig.map((item) => (
          <NavigationMenuItem key={item.name}>
            {item.href ? ( // This is a direct link
              <NavigationMenuLink href={item.href} className={getLinkClassName(item.href)}>
                {item.name}
              </NavigationMenuLink>
            ) : ( // This is a dropdown menu
              <>
                <NavigationMenuTrigger className={getTriggerClassName(item as { items: Array<{ href: string }> })}>
                  {item.triggerText}
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <div className="p-4 md:w-[400px] lg:w-[500px]">
                    <ul className="grid gap-3">
                      {item.items?.map((subItem) => (
                        <ListItem
                          key={subItem.title}
                          title={subItem.title}
                          href={subItem.href}
                          className={getLinkClassName(subItem.href, true)}
                        >
                          {subItem.description}
                        </ListItem>
                      ))}
                    </ul>
                  </div>
                </NavigationMenuContent>
              </>
            )}
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  ), [desktopNavConfig, getLinkClassName, getTriggerClassName]);
  
  // 使用useMemo缓存移动端导航菜单，防止页面切换时重新加载
  const memoizedMobileMenu = useMemo(() => (
    <>
      {/* 直接链接项 */}
      {desktopNavConfig.filter(item => 'href' in item).map((item) => (
        <Link
          key={item.name}
          href={item.href || '#'}
          className={`rounded-lg px-4 py-2 font-medium transition-colors ${
            pathname === item.href ? "bg-green-50 text-green-700" : "text-gray-700 hover:bg-gray-50"
          }`}
        >
          {item.name}
        </Link>
      ))}
      
      {/* 下拉菜单项在移动端展平显示 */}
      {desktopNavConfig.filter(item => !('href' in item)).map((category) => (
        <div key={category.name} className="mt-2">
          <div className="px-4 py-2 font-medium text-gray-500">{category.name}</div>
          <div className="ml-4 border-l border-gray-200 pl-4">
            {category.items?.map((subItem) => (
              <Link
                key={subItem.title}
                href={subItem.href || '#'}
                className={`block rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
                  pathname === subItem.href ? "bg-green-50 text-green-700" : "text-gray-700 hover:bg-gray-50"
                }`}
              >
                {subItem.title}
              </Link>
            ))}
          </div>
        </div>
      ))}
    </>
  ), [desktopNavConfig, pathname]);

  return (
    <header
      className={`fixed top-0 right-0 left-0 z-50 transition-all duration-300 ${
        isScrolled || needsSolidHeader ? "bg-white/95 py-3 shadow-md backdrop-blur-sm" : "bg-black/20 py-5 backdrop-blur-sm"
      }`}
    >
      <div className="container mx-auto flex items-center justify-between px-6">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3">
          <div
            className={`flex h-10 w-10 items-center justify-center rounded-full border-2 bg-white ${
              isScrolled || needsSolidHeader ? "border-yellow-500" : "border-yellow-400"
            } overflow-hidden shadow-md transition-all duration-300 hover:scale-105`}
          >
            <Image src="/favicon.ico" alt="IFMB Logo" width={24} height={24} className="object-contain" />
          </div>
          <h1
            className={`font-bold tracking-tight ${
              isScrolled || needsSolidHeader ? "text-gray-800" : "text-white"
            } transition-colors duration-300`}
          >
            <span className="hidden sm:inline">International Forum on </span>
            <span
              className={isScrolled || needsSolidHeader ? "text-amber-600" : "text-amber-400"}
              style={{ whiteSpace: "nowrap" }}
            >
              Maize Biology
            </span>
          </h1>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden items-center space-x-8 lg:flex">
          {memoizedDesktopMenu}

          {/* 用户状态显示 */}
          {isLoading ? (
            // 加载状态显示
            <div className="h-10 w-20 rounded-lg bg-white/10 animate-pulse"></div>
          ) : user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`flex items-center space-x-2 rounded-lg px-3 py-2 ${
                    isScrolled || needsSolidHeader ? "hover:bg-gray-100" : "hover:bg-white/10"
                  }`}
                >
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-white text-sm font-medium">
                    {user.name ? user.name.charAt(0).toUpperCase() : '?'}
                  </div>
                  <span className={`font-medium ${isScrolled || needsSolidHeader ? "text-gray-700" : "text-white"}`}>
                    {user.name || 'User'}
                  </span>
                  <i className={`fas fa-chevron-down text-xs ${isScrolled || needsSolidHeader ? "text-gray-500" : "text-white/70"}`}></i>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="px-3 py-2">
                  <p className="text-sm font-medium">{user.name || 'User'}</p>
                  <p className="text-xs text-gray-500">{user.email || 'No email'}</p>
                  {user.role !== 'admin' && (
                    <div className="mt-1 flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${user.emailVerified ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className={`text-xs ${user.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                        {user.emailVerified ? 'Email Verified' : 'Email Unverified'}
                      </span>
                    </div>
                  )}
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="cursor-pointer">
                    <i className="fas fa-user mr-2"></i>
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard" className="cursor-pointer">
                    <i className="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                  </Link>
                </DropdownMenuItem>
                {user.role === 'admin' && (
                  <DropdownMenuItem asChild>
                    <Link href="/admin" className="cursor-pointer">
                      <i className="fas fa-cog mr-2"></i>
                      Admin Panel
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout} className="cursor-pointer text-red-600">
                  <i className="fas fa-sign-out-alt mr-2"></i>
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Link href="/login">
              <Button
                className={`cursor-pointer rounded-lg font-medium ${
                  isScrolled || needsSolidHeader
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : "bg-white text-green-600 hover:bg-gray-50"
                } transform px-6 py-2 shadow-md transition-all duration-300 hover:-translate-y-1`}
              >
                Login
              </Button>
            </Link>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className={`lg:hidden ${isScrolled || needsSolidHeader ? "text-gray-700" : "text-white"}`}
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? <i className="fas fa-times text-2xl"></i> : <i className="fas fa-bars text-2xl"></i>}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="bg-white shadow-lg lg:hidden"
        >
          <div className="container mx-auto px-6 py-4">
            <nav className="flex flex-col space-y-4">
              {/* 使用预先缓存的移动端导航菜单 */}
              {memoizedMobileMenu}

              {/* 移动端用户状态 */}
              {isLoading ? (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="h-10 w-full rounded-lg bg-gray-100 animate-pulse"></div>
                </div>
              ) : user ? (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="flex items-center space-x-3 px-4 py-2 mb-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-600 text-white font-medium">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{user.name}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                      {user.role !== 'admin' && (
                        <div className="mt-1 flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${user.emailVerified ? 'bg-green-500' : 'bg-red-500'}`}></div>
                          <span className={`text-xs ${user.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                            {user.emailVerified ? 'Email Verified' : 'Email Unverified'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <Link href="/profile" className="block rounded-lg px-4 py-2 text-gray-700 hover:bg-gray-50">
                    <i className="fas fa-user mr-2"></i>
                    Profile
                  </Link>
                  <Link href="/dashboard" className="block rounded-lg px-4 py-2 text-gray-700 hover:bg-gray-50">
                    <i className="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                  </Link>
                  {user.role === 'admin' && (
                    <Link href="/admin" className="block rounded-lg px-4 py-2 text-gray-700 hover:bg-gray-50">
                      <i className="fas fa-cog mr-2"></i>
                      Admin Panel
                    </Link>
                  )}
                  <button
                    onClick={handleLogout}
                    className="w-full text-left rounded-lg px-4 py-2 text-red-600 hover:bg-red-50"
                  >
                    <i className="fas fa-sign-out-alt mr-2"></i>
                    Logout
                  </button>
                </div>
              ) : (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <Link href="/login" className="w-full">
                    <Button className="w-full rounded-lg bg-green-600 py-3 text-white hover:bg-green-700">
                      Login
                    </Button>
                  </Link>
                </div>
              )}
            </nav>
          </div>
        </motion.div>
      )}
    </header>
  )
});

export { Header }
