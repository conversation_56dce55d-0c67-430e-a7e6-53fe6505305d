"use client"

import { usePathname } from 'next/navigation';
import { useEffect, memo, useMemo } from 'react';
import { Header } from "@/components/layout/header";
import { UserProvider } from "@/components/layout/user-context";

// 在模块级别创建一个稳定的 Header 实例
const StableHeaderInstance = memo(() => <Header />);
StableHeaderInstance.displayName = 'StableHeaderInstance';

// 使用 memo 来防止不必要的重新渲染
const LayoutWrapper = memo(function LayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // 只在开发环境下输出调试信息
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`LayoutWrapper: MOUNTED. Pathname: ${pathname}`);
    }

    return () => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`LayoutWrapper: UNMOUNTED. Pathname: ${pathname}`);
      }
    };
  }, []); // 空依赖数组确保只在挂载和卸载时运行

  // 使用 useMemo 确保 Header 实例永远不会重新创建
  const stableHeader = useMemo(() => <StableHeaderInstance />, []);

  return (
    <UserProvider>
      {/* 使用稳定的 Header 实例，确保永远不会重新渲染 */}
      {stableHeader}
      <main>{children}</main>
    </UserProvider>
  );
});

export default LayoutWrapper;