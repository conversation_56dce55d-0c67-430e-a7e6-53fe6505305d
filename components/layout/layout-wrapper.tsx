"use client"

import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
// import { Header } from "@/components/layout/header"; // Header 仍然注释掉

export default function LayoutWrapper({ children }: { children: React.ReactNode }) {
  // 日志1: 确认函数体是否执行，以及执行时的路径
  console.log(`LayoutWrapper: FUNCTION BODY EXECUTED. Pathname during execution: ${usePathname()}`); 
  
  const pathname = usePathname();
  // 日志2: 确认 usePathname hook 返回的路径
  console.log(`LayoutWrapper: rendered or re-rendered. Pathname from hook: ${pathname}`);

  useEffect(() => {
    // 日志3: 确认组件挂载
    console.log(`LayoutWrapper: MOUNTED. Pathname on mount: ${pathname}`);
    
    return () => {
      // 日志4: 确认组件卸载。注意：这里的 pathname 是 effect 创建时捕获的 pathname
      console.log(`LayoutWrapper: UNMOUNTED. Pathname on unmount: ${pathname}`); 
    };
  }, []); // 空依赖数组确保只在挂载和卸载时运行

  return (
    <div>
      <p>Test</p> {/* 极简内容 */}
      <main>{children}</main>
    </div>
  );
}