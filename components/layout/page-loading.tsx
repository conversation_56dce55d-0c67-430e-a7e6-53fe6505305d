"use client"

import React from 'react'

// 页面加载占位符组件
export default function PageLoading() {
  return (
    <main className="pt-32 pb-20">
      {/* Hero Section Placeholder */}
      <section className="relative overflow-hidden pt-16 pb-16 bg-gradient-to-b from-blue-900/20 to-transparent">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-4xl text-center">
            {/* Badge placeholder */}
            <div className="mb-4 mx-auto w-24 h-6 bg-gray-200 rounded-full animate-pulse"></div>
            
            {/* Title placeholder */}
            <div className="mb-6 space-y-3">
              <div className="mx-auto w-96 h-12 bg-gray-200 rounded animate-pulse"></div>
              <div className="mx-auto w-80 h-12 bg-gray-200 rounded animate-pulse"></div>
            </div>
            
            {/* Description placeholder */}
            <div className="mb-8 space-y-2">
              <div className="mx-auto w-full max-w-2xl h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="mx-auto w-full max-w-xl h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>
            
            {/* Buttons placeholder */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <div className="w-40 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
              <div className="w-40 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
            </div>
            
            {/* Stats cards placeholder */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <div className="w-16 h-8 bg-gray-200 rounded animate-pulse mx-auto mb-2"></div>
                  <div className="w-24 h-4 bg-gray-200 rounded animate-pulse mx-auto"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Content Section Placeholder */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-4xl">
            {/* Section title placeholder */}
            <div className="text-center mb-12">
              <div className="mx-auto w-64 h-8 bg-gray-200 rounded animate-pulse mb-4"></div>
              <div className="mx-auto w-full max-w-2xl h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div className="mx-auto w-full max-w-xl h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>

            {/* Cards placeholder */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="p-6 border border-gray-200 rounded-lg">
                  <div className="w-full h-6 bg-gray-200 rounded animate-pulse mb-4"></div>
                  <div className="space-y-2">
                    <div className="w-full h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="w-3/4 h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="w-1/2 h-4 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Additional sections placeholder */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-6xl">
            {/* Section title */}
            <div className="text-center mb-12">
              <div className="mx-auto w-48 h-8 bg-gray-200 rounded animate-pulse mb-4"></div>
              <div className="mx-auto w-full max-w-lg h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>

            {/* Grid placeholder */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="w-full h-32 bg-gray-200 rounded animate-pulse mb-4"></div>
                  <div className="w-3/4 h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
                  <div className="w-full h-4 bg-gray-200 rounded animate-pulse mb-1"></div>
                  <div className="w-2/3 h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
