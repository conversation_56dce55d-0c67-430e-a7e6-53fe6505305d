"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { Header } from './header'

// 创建一个全局的Header上下文
const HeaderContext = createContext<{
  headerInstance: JSX.Element | null
  setHeaderInstance: (instance: JSX.Element | null) => void
}>({
  headerInstance: null,
  setHeaderInstance: () => {}
})

// Header提供者组件
export function HeaderProvider({ children }: { children: ReactNode }) {
  const [headerInstance, setHeaderInstance] = useState<JSX.Element | null>(null)

  // 在首次挂载时创建Header实例
  useEffect(() => {
    if (!headerInstance) {
      setHeaderInstance(<Header key="stable-header" />)
    }
  }, [headerInstance])

  return (
    <HeaderContext.Provider value={{ headerInstance, setHeaderInstance }}>
      {children}
    </HeaderContext.Provider>
  )
}

// 稳定的Header组件
export function StableHeader() {
  const { headerInstance } = useContext(HeaderContext)
  return headerInstance
}
