"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 用户类型定义
type User = {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'user' | 'admin'
  emailVerified?: boolean
}

// Context 类型定义
type UserContextType = {
  user: User | null
  setUser: (user: User | null) => void
  isLoading: boolean
  logout: () => void
}

// 创建 Context
const UserContext = createContext<UserContextType | undefined>(undefined)

// Provider 组件
export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // 检查用户登录状态 - 只在应用启动时执行一次
  useEffect(() => {
    const checkUserStatus = () => {
      const userData = localStorage.getItem('user')
      if (userData) {
        try {
          let parsedData;
          try {
            parsedData = JSON.parse(userData) as any;
          } catch (jsonError) {
            if (process.env.NODE_ENV === 'development') {
              console.error('Invalid JSON in localStorage:', jsonError);
            }
            localStorage.removeItem('user');
            setUser(null);
            setIsLoading(false);
            return;
          }
          
          const userInfo = parsedData.user_info || (parsedData.data && parsedData.data.user_info);
          
          if (userInfo && typeof userInfo === 'object' && 'id' in userInfo) {
            setUser(userInfo as User);
            if (process.env.NODE_ENV === 'development') {
              console.log('UserProvider: User loaded from localStorage', userInfo);
            }
          } else {
            if (process.env.NODE_ENV === 'development') {
              console.error('Invalid user data format in localStorage');
            }
            setUser(null);
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error parsing user data:', error);
          }
          setUser(null);
        }
      } else {
        setUser(null);
      }
      setIsLoading(false);
    }

    checkUserStatus()
  }, [])

  // 登出功能
  const logout = () => {
    localStorage.removeItem('user')
    setUser(null)
  }

  const value = {
    user,
    setUser,
    isLoading,
    logout
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

// Hook 来使用 Context
export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
