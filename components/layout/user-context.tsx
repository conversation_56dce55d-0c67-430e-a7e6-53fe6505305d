"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 用户类型定义
type User = {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'user' | 'admin'
  emailVerified?: boolean
}

// Context 类型定义
type UserContextType = {
  user: User | null
  setUser: (user: User | null) => void
  isHydrated: boolean
  logout: () => void
}

// 创建 Context
const UserContext = createContext<UserContextType | undefined>(undefined)

// Provider 组件
export function UserProvider({ children }: { children: ReactNode }) {
  // 为了避免 SSR 水合错误，初始状态始终为 null
  const [user, setUser] = useState<User | null>(null)
  const [isHydrated, setIsHydrated] = useState(false)

  // 在客户端水合后立即检查用户状态
  useEffect(() => {
    const checkUserStatus = () => {
      const userData = localStorage.getItem('user')
      if (userData) {
        try {
          let parsedData;
          try {
            parsedData = JSON.parse(userData) as any;
          } catch (jsonError) {
            if (process.env.NODE_ENV === 'development') {
              console.error('Invalid JSON in localStorage:', jsonError);
            }
            localStorage.removeItem('user');
            setUser(null);
            return;
          }

          const userInfo = parsedData.user_info || (parsedData.data && parsedData.data.user_info);

          if (userInfo && typeof userInfo === 'object' && 'id' in userInfo) {
            setUser(userInfo as User);
            if (process.env.NODE_ENV === 'development') {
              console.log('UserProvider: User loaded from localStorage', userInfo);
            }
          } else {
            if (process.env.NODE_ENV === 'development') {
              console.error('Invalid user data format in localStorage');
            }
            setUser(null);
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error parsing user data:', error);
          }
          setUser(null);
        }
      } else {
        setUser(null);
      }
      setIsHydrated(true);
    }

    checkUserStatus()
  }, [])

  // 监听 localStorage 变化（用于跨标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user') {
        if (e.newValue) {
          try {
            const parsedData = JSON.parse(e.newValue) as any;
            const userInfo = parsedData.user_info || (parsedData.data && parsedData.data.user_info);
            if (userInfo && typeof userInfo === 'object' && 'id' in userInfo) {
              setUser(userInfo as User);
            }
          } catch (error) {
            setUser(null);
          }
        } else {
          setUser(null);
        }
      }
    }

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [])

  // 登出功能
  const logout = () => {
    localStorage.removeItem('user')
    setUser(null)
  }

  const value = {
    user,
    setUser,
    isHydrated,
    logout
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

// Hook 来使用 Context
export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
