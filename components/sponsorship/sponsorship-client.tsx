"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { fadeIn, staggerContainer } from "@/lib/animations"

// Types
type ConferenceInfo = {
  title: string
  shortTitle: string
  dates: string
  location: string
  venue: string
  expectedAttendance: string
  organizers: string[]
}

type ExhibitionBooth = {
  id: string
  name: string
  dimensions: string
  price: number
  currency: string
  features: string[]
}

type PromotionalOption = {
  id: string
  name: string
  description?: string
  price: number
  currency: string
  unit?: string
}

type SponsorshipPackage = {
  id: string
  name: string
  price: number
  currency: string
  features: string[]
  highlighted?: boolean
}

type Contact = {
  name: string
  phone: string
  email: string
}

type PaymentInfo = {
  international: {
    beneficiary: string
    accountNumber: string
    bank: string
    swiftCode: string
    address: string
  }
  domestic: {
    beneficiary: string
    bank: string
    beneficiaryNumber: string
    account: string
    address: string
  }
}

type ImportantInfo = {
  deadline: string
  paymentDeadline: string
  totalBooths: string
  notes: string[]
}

type SponsorshipClientProps = {
  conferenceInfo: ConferenceInfo
  exhibitionBooths: ExhibitionBooth[]
  promotionalOptions: PromotionalOption[]
  sponsorshipPackages: SponsorshipPackage[]
  contacts: Contact[]
  paymentInfo: PaymentInfo
  importantInfo: ImportantInfo
}

export default function SponsorshipClient({
  conferenceInfo,
  exhibitionBooths,
  promotionalOptions,
  sponsorshipPackages,
  contacts,
  paymentInfo,
  importantInfo,
}: SponsorshipClientProps) {
  return (
    <main className="pb-20">
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-32 pb-16 text-white">
        <div className="absolute inset-0">
          <Image
            src="/images/background.jpg"
            alt="Conference Background"
            fill
            priority
            className="object-cover object-top"
          />
          <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-blue-900/30"></div>
          <div className="absolute inset-0 bg-[url('/images/pattern.svg')] opacity-10"></div>
        </div>
        <div className="relative z-10 container mx-auto px-6">
          <div className="mx-auto max-w-4xl p-8 text-center">
            <Badge className="mb-4 border-blue-400/50 bg-blue-600/40 px-3 py-1 text-white">
              {conferenceInfo.shortTitle}
            </Badge>
            <h1 className="mb-6 text-4xl font-bold md:text-5xl">
              Sponsorship & Exhibition
            </h1>
            <p className="text-lg text-blue-100 mb-8">
              Support the premier international conference on maize biology and showcase your products to leading researchers worldwide
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-2xl font-bold text-yellow-400">{conferenceInfo.expectedAttendance}</div>
                <div className="text-sm text-blue-100">Expected Participants</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-2xl font-bold text-yellow-400">{importantInfo.totalBooths.split(' ')[0]}</div>
                <div className="text-sm text-blue-100">Exhibition Booths</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-2xl font-bold text-yellow-400">5</div>
                <div className="text-sm text-blue-100">Days of Exhibition</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Conference Overview */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                About {conferenceInfo.title}
              </h2>
              <p className="text-lg text-gray-600">
                The International Forum on Maize Biology (IFMB) is a premier academic event in the field of maize biology, 
                dedicated to fostering collaboration and exchange among maize researchers worldwide and advancing fundamental 
                research and biotechnology development in maize.
              </p>
            </motion.div>

            <motion.div variants={fadeIn("up", 0.2)} className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <i className="fas fa-calendar-alt text-green-600 mr-3"></i>
                    Event Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Dates:</span>
                    <span className="font-medium">{conferenceInfo.dates}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Location:</span>
                    <span className="font-medium">{conferenceInfo.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Venue:</span>
                    <span className="font-medium">{conferenceInfo.venue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Attendance:</span>
                    <span className="font-medium">{conferenceInfo.expectedAttendance} participants</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center">
                    <i className="fas fa-users text-green-600 mr-3"></i>
                    Organizers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {conferenceInfo.organizers.map((organizer, index) => (
                      <li key={index} className="flex items-start">
                        <i className="fas fa-check text-green-600 mr-2 mt-1 text-sm"></i>
                        <span className="text-gray-700">{organizer}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Sponsorship Options */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-6xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Sponsorship Opportunities</h2>
              <p className="text-lg text-gray-600">
                Choose from our comprehensive sponsorship packages or create a custom solution
              </p>
            </motion.div>

            <Tabs defaultValue="packages" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="packages">Sponsorship Packages</TabsTrigger>
                <TabsTrigger value="booths">Exhibition Booths</TabsTrigger>
                <TabsTrigger value="promotional">Promotional Options</TabsTrigger>
              </TabsList>

              <TabsContent value="packages" className="mt-8">
                <PackagesSection sponsorshipPackages={sponsorshipPackages} />
              </TabsContent>

              <TabsContent value="booths" className="mt-8">
                <BoothsSection exhibitionBooths={exhibitionBooths} />
              </TabsContent>

              <TabsContent value="promotional" className="mt-8">
                <PromotionalSection promotionalOptions={promotionalOptions} />
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </section>

      {/* Important Information */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Important Information</h2>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div variants={fadeIn("up", 0.2)}>
                <Card className="p-6 bg-red-50 border-red-200">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-red-700">
                      <i className="fas fa-exclamation-triangle mr-3"></i>
                      Sponsorship Deadline
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600 mb-2">{importantInfo.deadline}</div>
                    <p className="text-gray-700 text-sm">
                      Submit the signed and stamped Sponsorship Registration Form to the listed contacts.
                      Full payment must be remitted within {importantInfo.paymentDeadline} after confirmation.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={fadeIn("up", 0.3)}>
                <Card className="p-6 bg-blue-50 border-blue-200">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-blue-700">
                      <i className="fas fa-info-circle mr-3"></i>
                      Exhibition Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600 mb-2">{importantInfo.totalBooths}</div>
                    <p className="text-gray-700 text-sm">
                      Limited exhibition space available. Booth locations allocated based on payment receipt order.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            <motion.div variants={fadeIn("up", 0.4)} className="mt-8">
              <Card className="p-6">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <i className="fas fa-clipboard-list text-green-600 mr-3"></i>
                    Special Notes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {importantInfo.notes.map((note, index) => (
                      <li key={index} className="flex items-start">
                        <i className="fas fa-check text-green-600 mr-3 mt-1 text-sm"></i>
                        <span className="text-gray-700">{note}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Contact Information</h2>
              <p className="text-lg text-gray-600">
                Get in touch with our sponsorship team for more information
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {contacts.map((contact, index) => (
                <motion.div key={contact.name} variants={fadeIn("up", 0.1 * (index + 2))}>
                  <Card className="p-6 text-center">
                    <div className="mb-4">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i className="fas fa-user text-green-600 text-2xl"></i>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">{contact.name}</h3>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-center">
                        <i className="fas fa-phone text-green-600 mr-3"></i>
                        <a href={`tel:${contact.phone}`} className="text-gray-700 hover:text-green-600">
                          {contact.phone}
                        </a>
                      </div>
                      <div className="flex items-center justify-center">
                        <i className="fas fa-envelope text-green-600 mr-3"></i>
                        <a href={`mailto:${contact.email}`} className="text-gray-700 hover:text-green-600">
                          {contact.email}
                        </a>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>

            <motion.div variants={fadeIn("up", 0.4)} className="mt-8 text-center">
              <Card className="p-6">
                <CardHeader>
                  <CardTitle className="flex items-center justify-center">
                    <i className="fas fa-map-marker-alt text-green-600 mr-3"></i>
                    Address
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">
                    Huazhong Agricultural University<br />
                    1 Shizishan St, Hongshan District<br />
                    Wuhan 430070, China
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Payment Information */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Payment Information</h2>
              <p className="text-lg text-gray-600">
                Bank details for international and domestic sponsors
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div variants={fadeIn("up", 0.2)}>
                <Card className="p-6">
                  <CardHeader>
                    <CardTitle className="flex items-center text-blue-700">
                      <i className="fas fa-globe text-blue-600 mr-3"></i>
                      International Sponsors
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="font-medium text-gray-600">Beneficiary:</span>
                      <div className="text-gray-900">{paymentInfo.international.beneficiary}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Account No.:</span>
                      <div className="text-gray-900 font-mono">{paymentInfo.international.accountNumber}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Bank:</span>
                      <div className="text-gray-900">{paymentInfo.international.bank}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">SWIFT Code:</span>
                      <div className="text-gray-900 font-mono">{paymentInfo.international.swiftCode}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Address:</span>
                      <div className="text-gray-900">{paymentInfo.international.address}</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={fadeIn("up", 0.3)}>
                <Card className="p-6">
                  <CardHeader>
                    <CardTitle className="flex items-center text-green-700">
                      <i className="fas fa-map-marker-alt text-green-600 mr-3"></i>
                      Domestic Sponsors
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="font-medium text-gray-600">Beneficiary:</span>
                      <div className="text-gray-900">{paymentInfo.domestic.beneficiary}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Bank:</span>
                      <div className="text-gray-900">{paymentInfo.domestic.bank}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Beneficiary No.:</span>
                      <div className="text-gray-900 font-mono">{paymentInfo.domestic.beneficiaryNumber}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Account:</span>
                      <div className="text-gray-900 font-mono">{paymentInfo.domestic.account}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Address:</span>
                      <div className="text-gray-900">{paymentInfo.domestic.address}</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-green-50">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl text-center"
          >
            <motion.div variants={fadeIn("up", 0.1)}>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Ready to Sponsor IFMB 2025?</h2>
              <p className="text-lg text-gray-600 mb-8">
                Join us in advancing maize biology research and connect with leading scientists from around the world.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg">
                  <i className="fas fa-download mr-2"></i>
                  Download Registration Form
                </Button>
                <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-50 px-8 py-4 text-lg">
                  <i className="fas fa-envelope mr-2"></i>
                  Contact Us
                </Button>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}

// Packages Section Component
function PackagesSection({ sponsorshipPackages }: { sponsorshipPackages: SponsorshipPackage[] }) {
  return (
    <motion.div initial="hidden" whileInView="show" viewport={{ once: true }} variants={staggerContainer(0.1)}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {sponsorshipPackages.map((pkg, index) => (
          <motion.div key={pkg.id} variants={fadeIn("up", 0.1 * (index + 1))}>
            <Card className="h-full">
              <CardHeader className="text-center">
                <CardTitle className="text-xl">{pkg.name}</CardTitle>
                <div className="text-3xl font-bold text-green-600">
                  ¥{pkg.price.toLocaleString()}
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <i className="fas fa-check text-green-600 mr-2 mt-1 text-sm"></i>
                      <span className="text-gray-700 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button className="w-full mt-6 bg-green-600 hover:bg-green-700">
                  Select Package
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

// Booths Section Component
function BoothsSection({ exhibitionBooths }: { exhibitionBooths: ExhibitionBooth[] }) {
  return (
    <motion.div initial="hidden" whileInView="show" viewport={{ once: true }} variants={staggerContainer(0.1)}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {exhibitionBooths.map((booth, index) => (
          <motion.div key={booth.id} variants={fadeIn("up", 0.1 * (index + 1))}>
            <Card className="h-full">
              <CardHeader className="text-center">
                <CardTitle>{booth.name}</CardTitle>
                <CardDescription>{booth.dimensions}</CardDescription>
                <div className="text-2xl font-bold text-green-600">
                  ¥{booth.price.toLocaleString()}
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {booth.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <i className="fas fa-check text-green-600 mr-2 mt-1 text-sm"></i>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button className="w-full mt-4 bg-green-600 hover:bg-green-700">
                  Book Booth
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}

// Promotional Section Component
function PromotionalSection({ promotionalOptions }: { promotionalOptions: PromotionalOption[] }) {
  return (
    <motion.div initial="hidden" whileInView="show" viewport={{ once: true }} variants={staggerContainer(0.1)}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {promotionalOptions.map((option, index) => (
          <motion.div key={option.id} variants={fadeIn("up", 0.1 * (index + 1))}>
            <Card className="h-full">
              <CardHeader className="text-center">
                <CardTitle className="text-lg">{option.name}</CardTitle>
                {option.description && (
                  <CardDescription>{option.description}</CardDescription>
                )}
                <div className="text-2xl font-bold text-green-600">
                  ¥{option.price.toLocaleString()}
                  {option.unit && <span className="text-sm text-gray-500"> {option.unit}</span>}
                </div>
              </CardHeader>
              <CardContent>
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  Add to Package
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}
