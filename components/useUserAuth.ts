"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'user' | 'admin';
  emailVerified?: boolean;
}

export function useUserAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoadingUser, setIsLoadingUser] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkUserStatus = () => {
      try {
        const userData = localStorage.getItem('user');
        if (userData) {
          let parsedData;
          try {
            parsedData = JSON.parse(userData) as any;
          } catch (jsonError) {
            console.error('Invalid JSON in localStorage:', jsonError);
            localStorage.removeItem('user');
            setUser(null);
            return;
          }

          const userInfo = parsedData.user_info || (parsedData.data && parsedData.data.user_info);

          if (userInfo && typeof userInfo === 'object' && 'id' in userInfo) {
            setUser(userInfo as User);
          } else {
            console.error('Invalid user data format in localStorage.');
            setUser(null); 
          }
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Error accessing localStorage or parsing user data:', error);
        setUser(null);
      }
    };

    checkUserStatus();
    setIsLoadingUser(false);
  }, []);

  const handleLogout = useCallback(() => {
    localStorage.removeItem('user');
    setUser(null);
    setIsLoadingUser(false); 
    router.push('/');
  }, [router]);

  return { user, isLoadingUser, handleLogout, setUser };
}
